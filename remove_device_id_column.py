#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to remove the device_id column from the SQLite database.
SQLite doesn't support DROP COLUMN directly, so we need to recreate the table.
"""

import sqlite3
import os

def remove_device_id_column():
    """Remove device_id column from blood_pressure_readings table in SQLite."""
    db_path = "./hypertension.db"
    
    if not os.path.exists(db_path):
        print(f"SQLite database not found at {db_path}")
        return False
    
    print("Removing device_id column from SQLite database...")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if device_id column exists
        cursor.execute("PRAGMA table_info(blood_pressure_readings)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if "device_id" not in columns:
            print("✓ device_id column doesn't exist, nothing to remove")
            conn.close()
            return True
        
        print("📋 Current columns:", columns)
        
        # Create backup of the table
        print("Creating backup table...")
        cursor.execute("""
            CREATE TABLE blood_pressure_readings_backup AS 
            SELECT * FROM blood_pressure_readings
        """)
        
        # Drop the original table
        print("Dropping original table...")
        cursor.execute("DROP TABLE blood_pressure_readings")
        
        # Recreate table without device_id column
        print("Recreating table without device_id column...")
        cursor.execute("""
            CREATE TABLE blood_pressure_readings (
                id INTEGER PRIMARY KEY,
                user_id INTEGER NOT NULL,
                systolic INTEGER,
                diastolic INTEGER,
                pulse INTEGER,
                reading_time DATETIME,
                notes TEXT,
                interpretation TEXT,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        """)
        
        # Copy data back (excluding device_id)
        print("Copying data back...")
        cursor.execute("""
            INSERT INTO blood_pressure_readings 
            (id, user_id, systolic, diastolic, pulse, reading_time, notes, interpretation)
            SELECT id, user_id, systolic, diastolic, pulse, reading_time, notes, interpretation
            FROM blood_pressure_readings_backup
        """)
        
        # Drop backup table
        print("Cleaning up backup table...")
        cursor.execute("DROP TABLE blood_pressure_readings_backup")
        
        # Commit changes
        conn.commit()
        conn.close()
        
        print("✓ Successfully removed device_id column from SQLite database")
        print("\n📊 Updated table structure:")
        
        # Show new structure
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute("PRAGMA table_info(blood_pressure_readings)")
        columns = cursor.fetchall()
        for col in columns:
            print(f"  - {col[1]} ({col[2]})")
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"✗ Failed to remove device_id column: {e}")
        # Try to restore from backup if it exists
        try:
            cursor.execute("DROP TABLE IF EXISTS blood_pressure_readings")
            cursor.execute("ALTER TABLE blood_pressure_readings_backup RENAME TO blood_pressure_readings")
            conn.commit()
            print("🔄 Restored from backup due to error")
        except:
            pass
        conn.close()
        return False

if __name__ == "__main__":
    if remove_device_id_column():
        print("\n🎉 device_id column removed successfully!")
        print("You can now run the migration script without column size issues.")
    else:
        print("\n❌ Failed to remove device_id column.")
