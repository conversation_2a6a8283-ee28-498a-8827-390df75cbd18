﻿services:
  toolbox:
    image: us-central1-docker.pkg.dev/database-toolbox/toolbox/toolbox:0.7.0
    ports:
      - "5000:5000"
    volumes:
      - ./app/advisor_agent/tools.yaml:/tools.yaml
    environment:
      - DATABASE_URL=${DATABASE_URL}
    command: /toolbox --tools-file /tools.yaml --address 0.0.0.0

  backend:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - AZURE_API_KEY=${AZURE_API_KEY}
      - AZURE_ENDPOINT=${AZURE_ENDPOINT}
      - AZURE_API_VERSION=${AZURE_API_VERSION}
      - AZURE_DEPLOYMENT=${AZURE_DEPLOYMENT}
      - AZURE_AI_PROJECT_ENDPOINT=${AZURE_AI_PROJECT_ENDPOINT}
      - HEALTH_ADVISOR_AGENT_ID=${HEALTH_ADVISOR_AGENT_ID}
      - <PERSON><PERSON><PERSON><PERSON>D<PERSON>_AGENT_ID=${KNOWLEDGE_AGENT_ID}
      - TOOLBOX_URL=http://toolbox:5000
      - AZURE_TENANT_ID=${AZURE_TENANT_ID}
      - AZURE_CLIENT_ID=${AZURE_CLIENT_ID}
      - AZURE_CLIENT_SECRET=${AZURE_CLIENT_SECRET}
    depends_on:
      - toolbox
