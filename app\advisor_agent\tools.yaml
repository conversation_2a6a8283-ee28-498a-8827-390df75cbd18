sources:
  my-sql-db:
    kind: "mssql"
    server: "cardiomed-ai-db-server.database.windows.net"
    database: "cardiomed-ai-db"
    username: "harold"
    password: "realControlissurgical@911"
    port: 1433
    encrypt: true
    trust_server_certificate: false

tools:
  get_user_profile:
    kind: "mssql-sql"
    source: "my-sql-db"
    description: "Get the user's health profile including target BP, medical conditions, and medications"
    statement: "SELECT * FROM users WHERE id = 1"

  get_bp_history:
    kind: "mssql-sql"
    source: "my-sql-db"
    description: "Get user's blood pressure history with notes, ordered by date"
    statement: "SELECT systolic, diastolic, pulse, reading_time, notes FROM blood_pressure_readings WHERE user_id = 1 ORDER BY reading_time DESC LIMIT 20"

toolsets:
    my_toolset:
        - get_user_profile
        - get_bp_history
