#!/usr/bin/env python3
"""
Migration script to transfer data from SQLite to Azure SQL Database.
This script will:
1. Create all tables in the Azure SQL Database
2. Transfer all data from SQLite to Azure SQL Database
3. Verify the data transfer
"""

import os
import sqlite3
import sys
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def get_sqlite_connection():
    """Get SQLite database connection."""
    sqlite_path = "./hypertension.db"
    if not os.path.exists(sqlite_path):
        print(f"SQLite database not found at {sqlite_path}")
        return None
    return sqlite3.connect(sqlite_path)

def get_azure_sql_engine():
    """Get Azure SQL Database engine."""
    database_url = os.getenv("DATABASE_URL")
    if not database_url:
        print("DATABASE_URL environment variable not found!")
        print("Please make sure you have set the DATABASE_URL in your .env file.")
        return None

    if database_url.startswith("sqlite"):
        print("DATABASE_URL is still pointing to SQLite. Please update it to point to Azure SQL Database.")
        return None

    # Try different driver options
    driver_options = [
        "ODBC+Driver+18+for+SQL+Server",
        "ODBC+Driver+17+for+SQL+Server",
        "ODBC+Driver+13+for+SQL+Server",
        "SQL+Server+Native+Client+11.0",
        "SQL+Server"
    ]

    for driver in driver_options:
        try:
            # Replace driver in URL
            test_url = database_url.replace("ODBC+Driver+18+for+SQL+Server", driver)
            print(f"Trying driver: {driver.replace('+', ' ')}")

            engine = create_engine(
                test_url,
                echo=False,
                pool_pre_ping=True,
                pool_recycle=300,
            )
            # Test connection
            with engine.connect() as conn:
                conn.execute(text("SELECT 1"))
            print(f"✓ Successfully connected to Azure SQL Database using {driver.replace('+', ' ')}")
            return engine
        except Exception as e:
            print(f"  ✗ Failed with {driver.replace('+', ' ')}: {str(e)[:100]}...")
            continue

    print("✗ Failed to connect with any available driver")
    print("\nTo fix this issue, you need to install Microsoft ODBC Driver for SQL Server:")
    print("1. Download from: https://docs.microsoft.com/en-us/sql/connect/odbc/download-odbc-driver-for-sql-server")
    print("2. Install ODBC Driver 18 for SQL Server")
    print("3. Run this script again")
    return None

def create_tables_in_azure_sql(engine):
    """Create all tables in Azure SQL Database using SQLAlchemy models."""
    try:
        # Import models to register them with Base
        from app import models
        from app.database import Base
        
        # Create all tables
        Base.metadata.create_all(bind=engine)
        print("✓ Successfully created tables in Azure SQL Database")
        return True
    except Exception as e:
        print(f"✗ Failed to create tables: {e}")
        return False

def get_table_data(sqlite_conn, table_name):
    """Get all data from a SQLite table."""
    try:
        cursor = sqlite_conn.cursor()
        cursor.execute(f"SELECT * FROM {table_name}")
        columns = [description[0] for description in cursor.description]
        rows = cursor.fetchall()
        return columns, rows
    except sqlite3.OperationalError as e:
        if "no such table" in str(e):
            print(f"  Table {table_name} does not exist in SQLite database")
            return None, None
        raise

def insert_data_to_azure_sql(engine, table_name, columns, rows):
    """Insert data into Azure SQL Database table."""
    if not rows:
        print(f"  No data to migrate for table {table_name}")
        return True
    
    try:
        with engine.connect() as conn:
            # Create parameterized insert statement
            placeholders = ", ".join([f":{col}" for col in columns])
            insert_sql = f"INSERT INTO {table_name} ({', '.join(columns)}) VALUES ({placeholders})"
            
            # Convert rows to list of dictionaries
            data_dicts = []
            for row in rows:
                row_dict = {}
                for i, col in enumerate(columns):
                    row_dict[col] = row[i]
                data_dicts.append(row_dict)
            
            # Execute insert
            conn.execute(text(insert_sql), data_dicts)
            conn.commit()
            
        print(f"  ✓ Migrated {len(rows)} rows to {table_name}")
        return True
    except Exception as e:
        print(f"  ✗ Failed to migrate data to {table_name}: {e}")
        return False

def migrate_data():
    """Main migration function."""
    print("Starting data migration from SQLite to Azure SQL Database...")
    print("=" * 60)
    
    # Get connections
    sqlite_conn = get_sqlite_connection()
    if not sqlite_conn:
        return False
    
    azure_engine = get_azure_sql_engine()
    if not azure_engine:
        sqlite_conn.close()
        return False
    
    # Create tables in Azure SQL
    if not create_tables_in_azure_sql(azure_engine):
        sqlite_conn.close()
        return False
    
    # List of tables to migrate (in order to respect foreign key constraints)
    tables_to_migrate = [
        "users",
        "blood_pressure_readings", 
        "medication_reminders",
        "bp_check_reminders",
        "doctor_appointment_reminders",
        "workout_reminders"
    ]
    
    print("\nMigrating data...")
    success_count = 0
    
    for table_name in tables_to_migrate:
        print(f"\nMigrating table: {table_name}")
        
        # Get data from SQLite
        columns, rows = get_table_data(sqlite_conn, table_name)
        if columns is None:
            continue
        
        # Insert data into Azure SQL
        if insert_data_to_azure_sql(azure_engine, table_name, columns, rows):
            success_count += 1
    
    sqlite_conn.close()
    
    print("\n" + "=" * 60)
    print(f"Migration completed! Successfully migrated {success_count} tables.")
    
    if success_count > 0:
        print("\n✓ Your data has been successfully migrated to Azure SQL Database!")
        print("✓ You can now run your application with the new database.")
        print("\nNext steps:")
        print("1. Test your application locally: uv run app/main.py")
        print("2. Update your Docker deployment if needed")
        print("3. Consider backing up your SQLite database as a precaution")
    
    return success_count > 0

if __name__ == "__main__":
    if migrate_data():
        sys.exit(0)
    else:
        print("\n✗ Migration failed. Please check the errors above and try again.")
        sys.exit(1)
