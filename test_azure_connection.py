#!/usr/bin/env python3
"""
Simple test script to verify Azure SQL Database connection.
"""

import os
import urllib.parse
from sqlalchemy import create_engine, text
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_connection():
    """Test Azure SQL Database connection with different approaches."""
    
    # Connection parameters
    server = "cardiomed-ai-db-server.database.windows.net"
    database = "cardiomed-ai-db"
    username = "harold"
    password = "realControlissurgical@911"
    
    print("Testing Azure SQL Database connection...")
    print(f"Server: {server}")
    print(f"Database: {database}")
    print(f"Username: {username}")
    print("=" * 50)
    
    # Method 1: URL encode the password to handle special characters
    encoded_password = urllib.parse.quote_plus(password)
    connection_string_1 = f"mssql+pyodbc://{username}:{encoded_password}@{server}:1433/{database}?driver=ODBC+Driver+18+for+SQL+Server&Encrypt=yes&TrustServerCertificate=no&Connection+Timeout=30"
    
    print("\nMethod 1: URL-encoded password")
    try:
        engine = create_engine(connection_string_1, echo=False)
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1 as test"))
            print("✓ Connection successful!")
            return engine
    except Exception as e:
        print(f"✗ Failed: {e}")
    
    # Method 2: Try with TrustServerCertificate=yes
    connection_string_2 = f"mssql+pyodbc://{username}:{encoded_password}@{server}:1433/{database}?driver=ODBC+Driver+18+for+SQL+Server&Encrypt=yes&TrustServerCertificate=yes&Connection+Timeout=30"
    
    print("\nMethod 2: URL-encoded password + TrustServerCertificate=yes")
    try:
        engine = create_engine(connection_string_2, echo=False)
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1 as test"))
            print("✓ Connection successful!")
            return engine
    except Exception as e:
        print(f"✗ Failed: {e}")
    
    # Method 3: Try with different driver
    connection_string_3 = f"mssql+pyodbc://{username}:{encoded_password}@{server}:1433/{database}?driver=ODBC+Driver+17+for+SQL+Server&Encrypt=yes&TrustServerCertificate=yes&Connection+Timeout=30"
    
    print("\nMethod 3: ODBC Driver 17 + TrustServerCertificate=yes")
    try:
        engine = create_engine(connection_string_3, echo=False)
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1 as test"))
            print("✓ Connection successful!")
            return engine
    except Exception as e:
        print(f"✗ Failed: {e}")
    
    # Method 4: Try without port specification
    connection_string_4 = f"mssql+pyodbc://{username}:{encoded_password}@{server}/{database}?driver=ODBC+Driver+18+for+SQL+Server&Encrypt=yes&TrustServerCertificate=yes&Connection+Timeout=30"
    
    print("\nMethod 4: No port + TrustServerCertificate=yes")
    try:
        engine = create_engine(connection_string_4, echo=False)
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1 as test"))
            print("✓ Connection successful!")
            return engine
    except Exception as e:
        print(f"✗ Failed: {e}")
    
    print("\n" + "=" * 50)
    print("All connection methods failed.")
    print("\nPossible issues:")
    print("1. Firewall settings - Azure SQL Database might be blocking your IP")
    print("2. Network connectivity issues")
    print("3. Server name or credentials incorrect")
    print("\nNext steps:")
    print("1. Check Azure SQL Database firewall rules")
    print("2. Add your current IP address to allowed IPs")
    print("3. Verify server name and credentials in Azure portal")
    
    return None

if __name__ == "__main__":
    engine = test_connection()
    if engine:
        print("\n🎉 Ready to proceed with data migration!")
    else:
        print("\n❌ Please resolve connection issues before proceeding.")
