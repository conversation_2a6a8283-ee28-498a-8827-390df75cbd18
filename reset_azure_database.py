#!/usr/bin/env python3
"""
Script to reset Azure SQL Database tables to match the updated SQLite structure.
This will drop all existing tables and recreate them.
"""

import os
from sqlalchemy import create_engine, text
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def get_azure_sql_engine():
    """Get Azure SQL Database engine."""
    database_url = os.getenv("DATABASE_URL")
    if not database_url:
        print("DATABASE_URL environment variable not found!")
        return None
    
    try:
        engine = create_engine(
            database_url,
            echo=False,
            pool_pre_ping=True,
            pool_recycle=300,
        )
        # Test connection
        with engine.connect() as conn:
            conn.execute(text("SELECT 1"))
        print("✓ Successfully connected to Azure SQL Database")
        return engine
    except Exception as e:
        print(f"✗ Failed to connect to Azure SQL Database: {e}")
        return None

def reset_database():
    """Drop all tables and recreate them with the updated structure."""
    print("Resetting Azure SQL Database...")
    print("=" * 50)
    
    engine = get_azure_sql_engine()
    if not engine:
        return False
    
    try:
        with engine.connect() as conn:
            # Drop all tables in reverse order (to handle foreign key constraints)
            tables_to_drop = [
                'workout_reminders',
                'doctor_appointment_reminders', 
                'bp_check_reminders',
                'medication_reminders',
                'blood_pressure_readings',
                'users'
            ]
            
            print("Dropping existing tables...")
            for table in tables_to_drop:
                try:
                    conn.execute(text(f"DROP TABLE IF EXISTS {table}"))
                    print(f"  ✓ Dropped {table}")
                except Exception as e:
                    print(f"  ⚠️  Could not drop {table}: {e}")
            
            conn.commit()
            print("✓ All tables dropped successfully")
            
        # Now recreate tables using SQLAlchemy models
        print("\nRecreating tables with updated structure...")
        try:
            from app import models
            from app.database import Base
            
            # Create all tables
            Base.metadata.create_all(bind=engine)
            print("✓ All tables recreated successfully")
            
            # Verify tables were created
            with engine.connect() as conn:
                result = conn.execute(text("""
                    SELECT TABLE_NAME 
                    FROM INFORMATION_SCHEMA.TABLES 
                    WHERE TABLE_TYPE = 'BASE TABLE'
                    ORDER BY TABLE_NAME
                """))
                tables = [row[0] for row in result.fetchall()]
                print(f"\n📊 Created tables: {', '.join(tables)}")
            
            return True
            
        except Exception as e:
            print(f"✗ Failed to recreate tables: {e}")
            return False
        
    except Exception as e:
        print(f"✗ Failed to reset database: {e}")
        return False

if __name__ == "__main__":
    print("⚠️  WARNING: This will delete ALL data in your Azure SQL Database!")
    response = input("Are you sure you want to continue? (yes/no): ")
    
    if response.lower() in ['yes', 'y']:
        if reset_database():
            print("\n🎉 Database reset completed successfully!")
            print("You can now run the migration script to transfer your SQLite data.")
        else:
            print("\n❌ Database reset failed.")
    else:
        print("Operation cancelled.")
