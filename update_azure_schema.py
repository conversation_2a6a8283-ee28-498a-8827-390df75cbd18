#!/usr/bin/env python3
"""
<PERSON>ript to update Azure SQL Database schema to fix column size issues.
"""

import os
from sqlalchemy import create_engine, text
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def get_azure_sql_engine():
    """Get Azure SQL Database engine."""
    database_url = os.getenv("DATABASE_URL")
    if not database_url:
        print("DATABASE_URL environment variable not found!")
        return None
    
    try:
        engine = create_engine(
            database_url,
            echo=False,
            pool_pre_ping=True,
            pool_recycle=300,
        )
        # Test connection
        with engine.connect() as conn:
            conn.execute(text("SELECT 1"))
        print("✓ Successfully connected to Azure SQL Database")
        return engine
    except Exception as e:
        print(f"✗ Failed to connect to Azure SQL Database: {e}")
        return None

def update_schema():
    """Update the database schema to fix column size issues."""
    print("Updating Azure SQL Database schema...")
    print("=" * 50)
    
    engine = get_azure_sql_engine()
    if not engine:
        return False
    
    try:
        with engine.connect() as conn:
            # Update device_id column size in blood_pressure_readings table
            print("Updating device_id column size...")
            conn.execute(text("""
                ALTER TABLE blood_pressure_readings 
                ALTER COLUMN device_id VARCHAR(200)
            """))
            conn.commit()
            print("✓ Updated device_id column to VARCHAR(200)")
            
        print("\n✓ Schema update completed successfully!")
        return True
        
    except Exception as e:
        print(f"✗ Failed to update schema: {e}")
        return False

if __name__ == "__main__":
    if update_schema():
        print("\n🎉 Schema updated! You can now run the migration script again.")
    else:
        print("\n❌ Schema update failed.")
