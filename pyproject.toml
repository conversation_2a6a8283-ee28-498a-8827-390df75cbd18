[project]
name = "cardiomed-ai-1-0-dev"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "azure-ai-projects>=1.0.0b11",
    "azure-identity>=1.23.0",
    "bcrypt>=4.3.0",
    "email-validator>=2.2.0",
    "fastapi>=0.115.12",
    "groq>=0.25.0",
    "openai>=1.79.0",
    "openpyxl>=3.1.5",
    "pandas>=2.2.3",
    "passlib>=1.7.4",
    "pillow>=11.2.1",
    "pydantic>=2.11.4",
    "python-dotenv>=1.1.0",
    "python-multipart>=0.0.20",
    "reportlab>=4.4.1",
    "requests>=2.32.3",
    "sqlalchemy>=2.0.41",
    "toolbox-core>=0.2.1",
    "uvicorn>=0.34.2",
]
